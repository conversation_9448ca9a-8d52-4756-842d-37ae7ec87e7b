{"version": "2.0.0", "tasks": [{"label": "run dev", "type": "shell", "command": "$env:PORT=4000; npm run dev;", "problemMatcher": []}, {"label": "run dev on mac", "type": "shell", "command": "PORT=4000 npm run dev;", "problemMatcher": []}, {"label": "run https", "type": "shell", "command": "cd ~; local-ssl-proxy --source 4001 --target 4000 --key cert.key --cert cert.crt", "problemMatcher": []}, {"label": "run https on mac", "type": "shell", "command": "cd ~/Projects/dev_docker_settings/certs; local-ssl-proxy --source 4001 --target 4000 --key localhost.key --cert localhost.crt", "problemMatcher": []}]}