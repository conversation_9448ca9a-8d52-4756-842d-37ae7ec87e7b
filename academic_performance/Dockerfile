FROM node:slim AS builder
WORKDIR /app
COPY package*.json ./
COPY .npmrc ./
COPY .env.local ./
COPY .env.development ./
COPY .env.production ./
COPY .env ./
RUN npm install
COPY . .
RUN npm run build

FROM node:slim AS runner
ENV NODE_ENV=production
WORKDIR /app
# COPY --from=builder /app/public ./publicß
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/next.config.ts ./next.config.ts
COPY --from=builder /app/src ./src
COPY --from=builder /app/tsconfig.json ./tsconfig.json
COPY --from=builder /app/.env ./.env
COPY --from=builder /app/.env.local ./.env.local
COPY --from=builder /app/.env.development ./.env.development
COPY --from=builder /app/.env.production ./.env.production
CMD ["npm", "start"]