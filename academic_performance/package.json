{"name": "user_management", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@areldevops/rlx_shared": "^1.0.1", "@headlessui/react": "^2.2.2", "axios": "^1.7.7", "next": "^15.1.4", "next-intl": "^3.26.5", "nookies": "^2.5.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.53.1", "react-icons": "^5.3.0", "react-toastify": "^10.0.6"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.15", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}