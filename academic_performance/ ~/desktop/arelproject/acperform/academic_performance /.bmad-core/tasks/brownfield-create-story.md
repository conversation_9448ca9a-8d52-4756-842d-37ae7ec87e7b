# Create Brownfield Story Task

## Purpose

Create a single user story for very small brownfield enhancements that can be completed in one focused development session. This task is for minimal additions or bug fixes that require existing system integration awareness.

## When to Use This Task

**Use this task when:**

- The enhancement can be completed in a single story
- No new architecture or significant design is required
- The change follows existing patterns exactly
- Integration is straightforward with minimal risk
- Change is isolated with clear boundaries

**Use brownfield-create-epic when:**

- The enhancement requires 2-3 coordinated stories
- Some design work is needed
- Multiple integration points are involved

**Use the full brownfield PRD/Architecture process when:**

- The enhancement requires multiple coordinated stories
- Architectural planning is needed
- Significant integration work is required

## Instructions

### 1. Quick Project Assessment

Gather minimal but essential context about the existing project:

**Current System Context:**

- [ ] Relevant existing functionality identified
- [ ] Technology stack for this area noted
- [ ] Integration point(s) clearly understood
- [ ] Existing patterns for similar work identified

**Change Scope:**

- [ ] Specific change clearly defined
- [ ] Impact boundaries identified
- [ ] Success criteria established

### 2. Story Creation

Create a single focused story following this structure:

#### Story Title

{{Specific Enhancement}} - Brownfield Addition

#### User Story

As a {{user type}},
I want {{specific action/capability}},
So that {{clear benefit/value}}.

#### Story Context

**Existing System Integration:**

- Integrates with: {{existing component/system}}
- Technology: {{relevant tech stack}}
- Follows pattern: {{existing pattern to follow}}
- Touch points: {{specific integration points}}

#### Acceptance Criteria

**Functional Requirements:**

1. {{Primary functional requirement}}
2. {{Secondary functional requirement (if any)}}
3. {{Integration requirement}}

**Integration Requirements:** 4. Existing {{relevant functionality}} continues to work unchanged 5. New functionality follows existing {{pattern}} pattern 6. Integration with {{system/component}} maintains current behavior

**Quality Requirements:** 7. Change is covered by appropriate tests 8. Documentation is updated if needed 9. No regression in existing functionality verified

#### Technical Notes

- **Integration Approach:** {{how it connects to existing system}}
- **Existing Pattern Reference:** {{link or description of pattern to follow}}
- **Key Constraints:** {{any important limitations or requirements}}

#### Definition of Done

- [ ] Functional requirements met
- [ ] Integration requirements verified
- [ ] Existing functionality regression tested
- [ ] Code follows existing patterns and standards
- [ ] Tests pass (existing and new)
- [ ] Documentation updated if applicable

### 3. Risk and Compatibility Check

**Minimal Risk Assessment:**

- **Primary Risk:** {{main risk to existing system}}
- **Mitigation:** {{simple mitigation approach}}
- **Rollback:** {{how to undo if needed}}

**Compatibility Verification:**

- [ ] No breaking changes to existing APIs
- [ ] Database changes (if any) are additive only
- [ ] UI changes follow existing design patterns
- [ ] Performance impact is negligible

### 4. Validation Checklist

Before finalizing the story, confirm:

**Scope Validation:**

- [ ] Story can be completed in one development session
- [ ] Integration approach is straightforward
- [ ] Follows existing patterns exactly
- [ ] No design or architecture work required

**Clarity Check:**

- [ ] Story requirements are unambiguous
- [ ] Integration points are clearly specified
- [ ] Success criteria are testable
- [ ] Rollback approach is simple

## Success Criteria

The story creation is successful when:

1. Enhancement is clearly defined and appropriately scoped for single session
2. Integration approach is straightforward and low-risk
3. Existing system patterns are identified and will be followed
4. Rollback plan is simple and feasible
5. Acceptance criteria include existing functionality verification

## Important Notes

- This task is for VERY SMALL brownfield changes only
- If complexity grows during analysis, escalate to brownfield-create-epic
- Always prioritize existing system integrity
- When in doubt about integration complexity, use brownfield-create-epic instead
- Stories should take no more than 4 hours of focused development work
