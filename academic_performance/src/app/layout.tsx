import type { Metadata } from "next";
import localFont from "next/font/local";
import "./globals.css";
import "./styles/rlx-shared.css";
import { ToastContainer } from "react-toastify";
import 'react-toastify/dist/ReactToastify.css';
import { AuthLayout, NavLink, Translate } from "@areldevops/rlx_shared/components";
import { FaDiagramProject, FaPlus, FaUser, FaUsers } from "react-icons/fa6";
import { getLocale, getMessages, getTranslations } from "next-intl/server";
import { NextIntlClientProvider } from "next-intl";
const geistSans = localFont({
  src: "./fonts/GeistVF.woff",
  variable: "--font-geist-sans",
  weight: "100 900",
});
const geistMono = localFont({
  src: "./fonts/GeistMonoVF.woff",
  variable: "--font-geist-mono",
  weight: "100 900",
});
export const metadata: Metadata = {
  title: "Rlx User Management",
  description: "Rlx User Management",
};
export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const locale = await getLocale();
  const messages = await getMessages();
  const t = await getTranslations();
  return (
    <html lang={locale}>
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
        <NextIntlClientProvider locale={locale} messages={messages}>
          <ToastContainer position="top-center" theme="dark" className="rlx-toast" />
          <AuthLayout moduleName={t("moduleName")} navLinks={
            <>
              <NavLink title={t("users")} icon={
                <FaUser title={t("users")} className="rlx-nav-link-icon" />
              } href="/users" />
              <NavLink title={t("roles")} icon={
                <FaUsers title={t("roles")} className="rlx-nav-link-icon" />
              } href="/roles" />
              <NavLink title={t("claimSchemas")} icon={
                <FaDiagramProject title={t("claimSchemas")} className="rlx-nav-link-icon" />
              } href="/claimschemas" />
              <NavLink title={t("createCriteria")} icon={
                <FaPlus title={t("createCriteria")} className="rlx-nav-link-icon" />
              } href="/dynamiccriteriaform" />
            </>
          }>
            {children}
            <Translate />
          </AuthLayout>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
