{"test": "test en", "dashboard": "Dashboard", "dashboardDescription": "Welcome to the dashboard", "moduleName": "User Management", "users": "Users", "roles": "Roles", "claimSchemas": "<PERSON><PERSON><PERSON>", "apps": "Apps", "loginCenter": "Login Center", "userManagement": "User Management", "organizationManagement": "Organization Management", "logout": "Logout", "areYouSure": "Are you sure?", "areYouSureLong": "Are you sure you want to delete this?", "yes": "Yes", "no": "No", "loading": "Loading...", "size": "Size", "page": "Page", "totalPage": "Total Page(s)", "totalItem": "Total Item(s)", "en": "English", "tr": "Turkish", "save": "Save", "cancel": "Cancel", "title": "Users", "newUser": "New User", "listUser": "User List", "username": "Username", "email": "Email", "phoneNumber": "Phone Number", "fullName": "Full Name", "role": "Role", "claims": "<PERSON><PERSON><PERSON>", "invalidMail": "Invalid email address", "disabled": "Disabled", "fUsername": "Filter by Name", "fMail": "Filter by <PERSON><PERSON>", "fPhone": "Filter by Phone Number", "fFullName": "Filter by Full Name", "fDisabled": "Filter by Disabled", "clearFilter": "Clear Filter", "editUser": "Edit User", "addRole": "Add Role", "delete": "Delete", "actions": "Actions", "noRoles": "No roles found for this user", "addClaim": "<PERSON><PERSON>", "type": "Type", "value": "Value", "noClaims": "No claims found for this user", "edit": "Edit", "noUsers": "No users found", "searchSelectRole": "Search and select a role", "searchRole": "Search role", "searchSelectClaim": "Search and select a claim", "searchClaimSchema": "Search claim schema", "or": "or", "addClaimManually": "Add claim manually", "add": "Add", "new": "New <PERSON><PERSON><PERSON>", "desc": "Description", "fType": "Filter by Type", "fValue": "Filter by Value", "fDesc": "Filter by Description", "del": "Delete", "clear": "Clear", "search": "Search", "name": "Name", "description": "Description", "showList": "Show List", "addNew": "Add New", "roleList": "Role List", "newRole": "New Role", "fName": "Filter by Name", "editRole": "Edit Role", "noClaimsFound": "No claims found", "noRolesFound": "No roles found", "claimSchemaList": "<PERSON><PERSON><PERSON>", "newClaimSchema": "New <PERSON><PERSON><PERSON>", "editClaimSchema": "Edit <PERSON><PERSON><PERSON>", "fDescription": "Filter by Description", "noClaimSchemasFound": "No claim schemas found", "createCriteria": "Create Criteria"}