import { FaChevronDown, FaChevronUp } from "react-icons/fa6";
import { TreeListDto } from "./TreeList.types"; // veya types klas<PERSON><PERSON>ün
import { useState } from "react";

interface UnitTreeProps {
  data: TreeListDto[];
  searchTerm: string;
  onSelect: (unit: TreeListDto) => void;
  selectedId?: string;
}

export function TreeList({ data, searchTerm, onSelect, selectedId }: UnitTreeProps) {
  const [openMenus, setOpenMenus] = useState<string[]>([]);
  const paddingLeftSize = 8;

  const toggleMenu = (id: string) => {
    setOpenMenus((prev) =>
      prev.includes(id) ? prev.filter((i) => i !== id) : [...prev, id]
    );
  };

  const filterMenu = (items: TreeListDto[], term: string): TreeListDto[] => {
  return items
    .map((item) => {
      const itemMatch = item.name.toLowerCase().includes(term.toLowerCase());

      if (itemMatch) {
        return item;
      }

      const filteredChildren = filterMenu(item.children, term);

      if (filteredChildren.length > 0) {
        return { ...item, children: filteredChildren };
      }

      return null;
    })
    .filter(Boolean) as TreeListDto[];
};


  const renderMenu = (items: TreeListDto[], level = 0): JSX.Element[] => {
    return items.map((item) => {
      const isOpen = openMenus.includes(item.id);
      const isLeaf = item.children.length === 0;
      const isSelected = selectedId === item.id;

      return (
        <div key={item.id} className="ml-2">
          <div
            className={`flex items-center justify-between cursor-pointer px-2 py-1 rounded ${
              isSelected ? "bg-emerald-100" : "hover:bg-gray-100"
            }`}
            onClick={() => {
              if (!isLeaf) toggleMenu(item.id);
              onSelect(item);
            }}
          >
            <span className="ml-2" style={{ paddingLeft: `${level * paddingLeftSize}px` }}>
              {item.name}
            </span>
            {!isLeaf &&
              (isOpen ? <FaChevronUp size={12} /> : <FaChevronDown size={12} />)}
          </div>
          {!isLeaf && isOpen && (
            <div className="ml-4 border-l border-gray-300 pl-2">
              {renderMenu(item.children, level + 1)}
            </div>
          )}
        </div>
      );
    });
  };

  return <>{renderMenu(filterMenu(data, searchTerm))}</>;
}
