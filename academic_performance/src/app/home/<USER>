'use client'

import { FaUsers } from "react-icons/fa6";
import { useEffect, useState } from "react";
import { UserDto } from "../models/dtos";
import { PageSchema } from "@areldevops/rlx_shared/components";
import { useTranslations } from "use-intl";
import { FaHome } from "react-icons/fa";

export default function Users() {
    const [pageMode, setPageMode] = useState("list");
    const [selectedUser, setSelectedUser] = useState<UserDto | undefined>();
    const t = useTranslations();

    return <PageSchema title={<> <FaHome /> {t("home")}</>}>
        <h1>Home</h1>
    </PageSchema>
}