import axios from 'axios';
import { NextRequest, NextResponse } from 'next/server';
import https from "https";
import { getCookie } from '@areldevops/rlx_shared/helpers';
import { appConfig } from '@/app/appconfig';

export async function GET(request: NextRequest) {
    const action = request.headers.get('action');
    let path = '';
    console.log("action: " + action);
    switch (action) {
        case 'test':
            path = '/rlxclaimschema/test';
            break;
        case 'getuserroles':
            path = '/rlxuser/getuserroles';
            break;
        case 'getuserclaims':
            path = '/rlxuser/getuserclaims';
            break;
        case 'getroleclaims':
            path = '/rlxrole/getroleclaims';
            break;
        case 'getclaimschemasforautocomplete':
            path = '/rlxclaimschema/getclaimschemasforautocomplete';
            break;
        default:
            return NextResponse.json({ message: 'Undefined action! GET ' + action }, { status: 500 });
    }
    console.log("path: " + path);
    const queryString = createQueryString(request);
    console.log("queryString: " + queryString);
    const agent = new https.Agent({ rejectUnauthorized: false });
    const token = getCookie(request, 'rlx-access-token');
    return axios.get(appConfig.identityUrl + path + queryString, {
        httpsAgent: agent, withCredentials: true, headers: {
            'Authorization': `Bearer ${token}`
        }
    })
        .then((response) => {
            return NextResponse.json(response.data);
        })
        .catch((error) => {
            return NextResponse.json({ error }, { status: error.status });
        });
}
export async function POST(request: NextRequest) {
    console.log('api call a girdi');
    const body = await request.json();
    const action = request.headers.get('action');
    let path = '';
    switch (action?.toLowerCase()) {
        case 'createclaimschema':
            path = '/rlxclaimschema/createclaimschema';
            break;
        case 'getclaimschemas':
            path = '/rlxclaimschema/getclaimschemas';
            break;
        case 'getroles':
            path = '/rlxrole/getroles';
            break;
        case 'createrole':
            path = '/rlxrole/createrole';
            break;
        case 'getusers':
            path = '/rlxuser/getusers';
            break;
        case 'createuser':
            path = '/rlxuser/createuser';
            break;
        case 'getrolesforautocomplete':
            path = '/rlxrole/getrolesforautocomplete';
            break;
        case 'adduserrole':
            path = '/rlxuser/saveuserroles';
            break;
        case 'adduserclaim':
            path = '/rlxuser/saveuserclaims';
            break;
        case 'addroleclaim':
            path = '/rlxrole/saveroleclaims';
            break;
        case 'getlocalizations':
            path = '/localization/getlocalizations'; 
            break;
        case 'savelocalizations':
            path = '/localization/savelocalizations';
            break;
        default:
            return NextResponse.json({ message: 'Undefined action! POST ' + action }, { status: 500 });
    }
    const agent = new https.Agent({ rejectUnauthorized: false });
    const token = getCookie(request, 'rlx-access-token');
    return axios.post(appConfig.identityUrl + path, body, {
        httpsAgent: agent, withCredentials: true, headers: {
            'Authorization': `Bearer ${token}`
        }
    })
        .then((response) => {
            // console.log('response: ' + response)
            return NextResponse.json(response.data);
        })
        .catch((error) => {
            // const handledError = handleError(error);
            return NextResponse.json(error.response?.data, { status: error.status });
        });
    // return await rlxPost(host + path, body);
}
export async function PUT(request: NextRequest) {
    console.log('api call a girdi');
    const body = await request.json();
    // return;
    const action = request.headers.get('action');
    let path = '';
    switch (action?.toLowerCase()) {
        case 'updateclaimschema':
            path = '/rlxclaimschema/updateclaimschema';
            break;
        case 'updaterole':
            path = '/rlxrole/updaterole';
            break;
        case 'updateuser':
            path = '/rlxuser/updateuser';
            break;
        default:
            return NextResponse.json({ message: 'Undefined action! POST ' + action }, { status: 500 });
    }
    const agent = new https.Agent({ rejectUnauthorized: false });
    const token = getCookie(request, 'rlx-access-token');
    return axios.put(appConfig.identityUrl + path, body, {
        httpsAgent: agent, withCredentials: true,
        headers: {
            'Authorization': `Bearer ${token}`
        }
    })
        .then((response) => {
            // console.log('response: ' + response)
            return NextResponse.json(response.data);
        })
        .catch((error) => {
            // const handledError = handleError(error);
            return NextResponse.json(error.response?.data, { status: error.status });
        });
}
const createQueryString = (req: NextRequest) => {
    const queryString = new URL(req.url, `http://${req.headers.get('host')}`).search;
    return queryString;
}
