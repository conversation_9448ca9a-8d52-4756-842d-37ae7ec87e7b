import axios from 'axios';
import { NextRequest, NextResponse } from 'next/server';
import https from "https";
import { getCookie } from '@areldevops/rlx_shared/helpers';
import { appConfig } from '@/app/appconfig';
type Context = {
    params: {
        id: string
    }
}
export async function DELETE(
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
) {
    const { id } = await params;
    const action = request.headers.get('action');
    let path = '';
    switch (action?.toLowerCase()) {
        case 'deleteclaimschema':
            path = '/rlxclaimschema/deleteclaimschema/' + id;
            break;

        default:
            return NextResponse.json({ message: 'Undefined action! DELETE ' + action }, { status: 500 });
    }
    const agent = new https.Agent({ rejectUnauthorized: false });
    const token = getCookie(request, 'rlx-access-token');
    return axios.delete(appConfig.identityUrl + path, {
        httpsAgent: agent, withCredentials: true, headers: {
            'Authorization': `Bearer ${token}`
        }
    })
        .then((response) => {
            return NextResponse.json(response.data);
        })
        .catch((error) => {
            return NextResponse.json({ error }, { status: error.status });
        });
    // return await rlxDelete(host + path);
}
