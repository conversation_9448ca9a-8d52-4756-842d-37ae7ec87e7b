'use client'
import axios from "axios";
import { handleError } from "@areldevops/rlx_shared/helpers"
import { toast } from "react-toastify";
const clientCallGet = (qs: string, action: string, successFunc?: Function, errorFunc?: Function) => {
    axios.get(`/api/call` + qs, { headers: { 'action': action } })
        .then((response) => {
            if (successFunc) {
                successFunc(response.data);
            }
        })
        .catch((error) => {
            console.error(error);
            const handledError = handleError(error);
            console.log('handledError: ' + handledError);
            if (errorFunc) {
                errorFunc(error);
            }
            else {
                toast.error(handledError);
            }
        });
}
const clientCallPost = (body: any, action: string, successFunc?: Function, errorFunc?: Function) => {
    axios.post(`/api/call`, JSON.stringify(body), { headers: { 'action': action } })
        .then((response) => {
            if (successFunc) {
                successFunc(response.data);
            }
        })
        .catch((error) => {
            console.error(error);
            const handledError = handleError(error);
            console.log('handledError: ' + handledError);
            if (errorFunc) {
                errorFunc(error);
            }
            else {
                toast.error(handledError);
            }
        });
}
const clientCallPut = (body: any, action: string, successFunc?: Function, errorFunc?: Function) => {
    axios.put(`/api/call`, JSON.stringify(body), { headers: { 'action': action } })
        .then((response) => {
            if (successFunc) {
                successFunc(response.data);
            }
        })
        .catch((error) => {
            console.error(error);
            const handledError = handleError(error);
            console.log('handledError: ' + handledError);
            if (errorFunc) {
                errorFunc(error);
            }
            else {
                toast.error(handledError);
            }
        });
}
const clientCallDelete = (id: string, action: string, successFunc?: Function, errorFunc?: Function) => {
    axios.delete(`/api/call/` + id, { headers: { 'action': action } })
        .then((response) => {
            if (successFunc) {
                successFunc(response.data);
            }
        })
        .catch((error) => {
            console.error(error);
            const handledError = handleError(error);
            console.log('handledError: ' + handledError);
            if (errorFunc) {
                errorFunc(error);
            }
            else{
                toast.error(handledError);
            }
        });
}
export { clientCallGet, clientCallPost, clientCallPut, clientCallDelete }