import React, { useState } from 'react';
import { FaPlus } from 'react-icons/fa6';

export default function AcademicReportCard() {
  return (
    <div className="p-6 bg-gray-50 text-sm space-y-6">
      {/* Üst Bilgiler */}
      <div className="bg-white shadow rounded p-4">
        <div className="grid grid-cols-2 gap-4">
          <div><strong>Adı Soyadı:</strong> Taylan Ekin KARA</div>
          <div><strong>Fakülte – Bölüm / Anabilim Dalı:</strong> Yönetim Bilişim Sistemleri Geliştirme – Bilgi Teknolojileri Ofisi</div>
          <div><strong><PERSON><PERSON><PERSON> (Varsa):</strong> Yazılım Geliştirme Uzmanı</div>
          <div><strong>Değerlendirme Skoru:</strong> 0</div>
          <div><strong>Ders <PERSON>ükü:</strong> 0</div>
          <div><strong><PERSON><PERSON><PERSON>:</strong> 0</div>
        </div>
        <button className="mt-4 rlx-button-edit">
          veri girilmesi gereken kriterleri göster
        </button>
      </div>

      {/* Eğitim/Öğretim Faaliyetleri Tablosu */}
      <div className="overflow-x-auto">
        <table className="min-w-full border text-xs">
          <thead className="bg-orange-200 text-gray-700">
            <tr>
              <th className="border px-2 py-1 text-left">Faaliyet Türü</th>
              <th className="border px-2 py-1 w-12">Sayı</th>
              <th className="border px-2 py-1 w-12">Katsayı</th>
              <th className="border px-2 py-1 w-12">Puan</th>
              <th className="border px-2 py-1 w-12"></th>
            </tr>
          </thead>
          <tbody>
            <tr className="bg-orange-100 font-semibold">
              <td colSpan={5} className="px-2 py-1">A. Eğitim/Öğretim Faaliyetleri (30%)</td>
            </tr>

            {[1, 2, '3.1', '3.2', '3.3', '3.4', '3.5'].map((item, index) => (
              <tr key={index} className="even:bg-gray-50">
                <td className="border px-2 py-1">{item}</td>
                <td className="border px-2 py-1 text-center">0</td>
                <td className="border px-2 py-1 text-center">-</td>
                <td className="border px-2 py-1 text-center">-</td>
                <td className="border px-2 py-1 text-center">
                  <button className="text-green-600 hover:text-green-800">
                    <FaPlus />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Diğer Faaliyetler Tablosu */}
      <div className="overflow-x-auto">
        <table className="rlx-table">
            <tr className="even:bg-gray-50">
                <td className="border px-2 py-1 text-gray-700"><b>Kurs Seminer</b></td>
                <td className="border px-2 py-1 text-center">
                <button
                    onClick={() => console.log("add module")}
                    className="text-green-600 hover:text-green-800"
                >
                    <FaPlus />
                </button>
                </td>
            </tr>
        </table>
        <table className="rlx-table min-w-full">
            <thead className="">
                <tr>
                    <td>Onay Durumu</td>
                    <td>Sertifika Adı</td>
                    <td>Sertifika Tarihi</td>
                    <td>Sertifika Yeri</td>
                    <td>Dosya</td>
                </tr>
            </thead>
            <tbody>
                
            </tbody>
        </table>
      </div>
    </div>
  );
}
