import React, { useState } from 'react';

const initialCriteria = [
  { id: 1, name: "Ulusal hakemli dergilerde yayınlanmış makale sayısı", coefficient: 10 },
  { id: 2, name: "SCI, SSCI, AHCI indeksli dergilerde yayınlanmış makale", coefficient: 15 },
  { id: 3, name: "Uluslararası kongrelerde sunulan bildiri", coefficient: 8 }
];

export default function CriteriaTemplates() {
  const [criteria, setCriteria] = useState(initialCriteria);

  const handleEdit = (id: any) => {
    alert(`Kriter düzenle: ${id}`);
  };

  const handleDelete = (id: any) => {
    setCriteria(criteria.filter(c => c.id !== id));
  };

  const handleAdd = () => {
    const newId = Date.now();
    setCriteria([
      ...criteria,
      { id: newId, name: "Yeni Kriter", coefficient: 0 }
    ]);
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">Dinamik Kriter Şablonları</h1>
        <button
          onClick={handleAdd}
          className="rlx-button-save"
        >
          + Dinamik Kriter Ekle
        </button>
      </div>

      <div className="bg-white shadow-md rounded-lg overflow-x-auto">
        <table className="min-w-full text-sm text-left rlx-table">
          <thead className="bg-blue-50 text-gray-700 text-base">
            <tr>
              <th className="px-6 py-3 font-semibold">Kriter Adı</th>
              <th className="px-6 py-3 font-semibold text-center">Katsayı</th>
              <th className="px-6 py-3 font-semibold text-center">İşlemler</th>
            </tr>
          </thead>
          <tbody>
            {criteria.map((item) => (
              <tr key={item.id} className="border-t">
                <td className="px-6 py-4">{item.name}</td>
                <td className="px-6 py-4 text-center font-bold">{item.coefficient}</td>
                <td className="px-6 py-4 text-center">
                  <div className="flex justify-center space-x-2">
                    <button className="rlx-button-cancel">
                      Kriteri yinele
                    </button>
                    <button
                      onClick={() => handleEdit(item.id)}
                      className="rlx-button-edit"
                    >
                      Düzenle
                    </button>
                    <button
                      onClick={() => handleDelete(item.id)}
                      className="rlx-button-delete"
                    >
                      Sil
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
