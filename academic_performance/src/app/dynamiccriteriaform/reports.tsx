import React, { useState } from 'react';
import { FaSearch } from 'react-icons/fa';
import {
  FaChartColumn,
  FaFolder,
  FaFolderOpen,
  FaFileLines
} from "react-icons/fa6";

const reportData = [
  {
    title: "Genel İstatistikler",
    reports: [
      "Akademik Birimlere Göre Öğretim Elemanı Sayısı",
      "Birimlere Göre İdari Personel Sayısı",
      "Üniversitelerarası Kurul (UAK) Temel Alanlarına Göre Araştırmacı Sayısı",
      "Üniversitelerarası Kurul (UAK) Bilim Alanlarına Göre Araştırmacı Sayısı",
      "Üniversitelerarası Kurul (UAK) Anahtar Kelimelere Göre Araştırmacı Sayısı",
      "Cinsiyetlere Göre Öğretim Elemanı Sayısı",
      "Akademik Birimlere Göre Öğretim <PERSON>ş Ortalaması",
      "Unvanlara Göre Öğretim <PERSON>ı <PERSON>l Sayıları",
      "Yurt <PERSON>ışında Eğitim Alan Öğretim Elemanı Sayıları",
    ],
  },
  { title: "Bilimsel Yayınlar", reports: [] },
  { title: "Yayınlara Yapılan Atıflar", reports: [] },
  { title: "Proje İstatistikleri", reports: [] },
  { title: "Bilimsel Faaliyet İstatistikleri", reports: [] },
  { title: "Ödül İstatistikleri", reports: [] },
  { title: "Fikri Mülkiyet İstatistikleri", reports: [] },
  { title: "Performans Özeti", reports: [] },
];

export default function Reports() {
  const [openIndex, setOpenIndex] = useState(0);
  const [search, setSearch] = useState("");

  const toggleIndex = (index: any) => {
    setOpenIndex(index === openIndex ? null : index);
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold text-blue-700 flex items-center gap-2">
          <FaChartColumn />
          RAPORLAR
        </h1>
        <div className="relative">
          <input
            type="text"
            placeholder="Rapor ara..."
            className="pl-10 pr-4 py-2 border rounded-full text-sm shadow-sm focus:outline-none focus:ring focus:border-blue-300 w-64"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />
          <FaSearch className="absolute left-3 top-2.5 text-gray-400" />
        </div>
      </div>

      <div className="space-y-2">
        {reportData.map((category, index) => (
          <div key={index} className="bg-white rounded-lg shadow">
            <button
              onClick={() => toggleIndex(index)}
              className="w-full flex justify-between items-center px-4 py-3 text-left font-semibold text-gray-800 hover:bg-gray-100"
            >
              <span className="flex items-center gap-2">
                {openIndex === index ? <FaFolderOpen /> : <FaFolder />}
                {category.title}
              </span>
              <span className="text-sm text-gray-500">
                {openIndex === index ? "▲" : "▼"}
              </span>
            </button>

            {openIndex === index && category.reports.length > 0 && (
              <div className="p-4 flex flex-wrap gap-2">
                {category.reports
                  .filter((r) => r.toLowerCase().includes(search.toLowerCase()))
                  .map((report, rIdx) => (
                    <button
                      key={rIdx}
                      className="bg-blue-100 hover:bg-blue-200 text-blue-700 px-3 py-1 rounded-full text-sm flex items-center gap-2"
                    >
                      <FaFileLines />
                      {report}
                    </button>
                  ))}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
