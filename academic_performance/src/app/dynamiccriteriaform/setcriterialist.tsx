import React, { useState } from 'react';
import { FaPlus, FaTimes } from 'react-icons/fa';

export default function SetCriteriaList() {
    type Item = {
        id: string;
        title: string;
        description: string;
        inputs: any;
        katsayi: number;
      };
      
      type Category = {
        id: string;
        name: string;
        items: Item[];
      };
      
      const [categories, setCategories] = useState<Category[]>([
    {
      id: 'A',
      name: 'Eğitim/Öğretim Faaliyetleri',
      items: [
        { id: 'A.1', title: 'Lisans/Lisansüstü Ders Verme', katsayi: 2.5, description: 'Akademik dönem içerisinde verilen derslerin değerlendirilmesi', inputs: [{ name: '<PERSON><PERSON>', type: 'number', min: 1, max: 10 }, { name: '<PERSON><PERSON>', type: 'number', min: 1, max: 40 }] },
        { id: 'A.2', title: 'Ders Materyali Hazırlama', katsayi: 1.8, description: '', inputs: [] },
      ],
    },
    {
      id: 'B',
      name: 'Akademik ve Bilimsel Faaliyetler',
      items: [
        { id: 'B.1', title: 'Uluslararası Makale Yayını', katsayi: 3.0, description: '', inputs: [] },
        { id: 'B.2', title: 'Ulusal Konferans Bildirisi', katsayi: 1.5, description: '', inputs: [] },
      ],
    },
  ]);

  const [modalVisible, setModalVisible] = useState(false);
  const [selectedItem, setSelectedItem] = useState<Item | null>(null);

  const openModal = (item: any) => {
    setSelectedItem(item);
    setModalVisible(true);
  };

  const closeModal = () => {
    setSelectedItem(null);
    setModalVisible(false);
  };

  const renderItem = (item: Item) => (
    <div
      key={item.id}
      className="flex items-start justify-between bg-white rounded p-2 border mb-2"
    >
      <div>
        <div className="font-semibold">
          <span className="inline-block w-8 text-center text-xs bg-gray-200 rounded-full mr-2">
            {item.id}
          </span>
          {item.title}
        </div>
        <div className="text-xs text-gray-600">Katsayı: {item.katsayi}</div>
      </div>
      <div className="flex items-center gap-2">
        <button
          className="rlx-button-edit"
          onClick={() => openModal(item)}
        >
          Detaylar
        </button>
        <button className="rlx-button-delete">
          <FaTimes />
        </button>
      </div>
    </div>
  );

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-xl font-bold">Kriter Listesi Ayarla</h1>

      <button className="rlx-button-save">
      <FaPlus /> Kategori ekle 
      </button>

      {categories.map((cat) => (
        <div key={cat.id} className="bg-gray-100 border rounded mt-4">
          <div className="flex items-center justify-between p-2 border-b">
            <div className="text-lg font-semibold">
              {cat.id}. {cat.name}
            </div>
            <div className="flex items-center gap-2">
              <button className="rlx-button-edit">
                Kategoriyi düzenle
              </button>
              <button className="rlx-button-save">
                <FaPlus />
              </button>
            </div>
          </div>
          <div className="p-2">{cat.items.map(renderItem)}</div>
        </div>
      ))}

      {/* Modal */}
      {modalVisible && selectedItem && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded p-6 max-w-lg w-full relative">
            <button
              className="absolute top-2 right-2 text-black"
              onClick={closeModal}
            >
              <FaTimes />
            </button>
            <h2 className="text-lg font-bold mb-2">Kriter Detayları</h2>
            <div className="font-semibold">{selectedItem.title}</div>
            <p className="text-sm mb-1">
              <strong>Açıklama:</strong> {selectedItem.description || '—'}
            </p>
            <p className="text-sm mb-2">
              <strong>Katsayı:</strong> {selectedItem.katsayi}
            </p>

            {selectedItem.inputs.length > 0 && (
              <>
                <div className="text-sm font-semibold">Input Alanları:</div>
                <ul className="list-disc ml-6 text-sm">
                  {selectedItem.inputs.map((input: any, idx: any) => (
                    <li key={idx}>
                      <strong>{input.name}</strong> <br />
                      Tip: {input.type} <br />
                      Kısıtlar: Min: {input.min}, Max: {input.max}
                    </li>
                  ))}
                </ul>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
