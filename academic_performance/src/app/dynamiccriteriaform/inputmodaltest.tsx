import React, { useState } from 'react';
import { Dialog } from '@headlessui/react';

export default function InputModalTest() {
  const [isOpen, setIsOpen] = useState(false);
  const [isMultiplier, setIsMultiplier] = useState(false);

  return (
    <div className="p-6">
      <button
        onClick={() => setIsOpen(true)}
        className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
      >
        Yeni Input Ekle
      </button>

      <Dialog open={isOpen} onClose={() => setIsOpen(false)} className="relative z-50">
        <div className="fixed inset-0 bg-black/30" aria-hidden="true" />

        <div className="fixed inset-0 flex items-center justify-center p-4">
          <Dialog.Panel className="w-full max-w-lg bg-white rounded-lg p-6 shadow-lg space-y-4">
            <Dialog.Title className="text-xl font-bold border-b pb-2">Input Ekle</Dialog.Title>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Input Adı:</label>
                <input
                  type="text"
                  className="mt-1 w-full border rounded px-3 py-2"
                  placeholder="Ad girin"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Input Tipi:</label>
                <select className="mt-1 w-full border rounded px-3 py-2">
                  <option value="sayi">Sayı</option>
                  <option value="metin">Metin</option>
                  <option value="tarih">Tarih</option>
                </select>
              </div>

              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={isMultiplier}
                  onChange={() => setIsMultiplier(!isMultiplier)}
                  className="w-4 h-4"
                />
                <label className="text-sm text-gray-800">
                  Çarpana katılacak mı? <span className="text-gray-500">(sadece 1 adet seçilebilir)</span>
                </label>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Minimum Değer:</label>
                <input
                  type="number"
                  className="mt-1 w-full border rounded px-3 py-2"
                  placeholder="0"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Maksimum Değer:</label>
                <input
                  type="number"
                  className="mt-1 w-full border rounded px-3 py-2"
                  placeholder="100"
                />
              </div>

              <div className="flex justify-end gap-2 pt-4 border-t mt-4">
                <button
                  onClick={() => setIsOpen(false)}
                  className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
                >
                  İptal
                </button>
                <button
                  onClick={() => {
                    // Kaydetme işlemleri
                    setIsOpen(false);
                  }}
                  className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
                >
                  Ekle
                </button>
              </div>
            </div>
          </Dialog.Panel>
        </div>
      </Dialog>
    </div>
  );
}
