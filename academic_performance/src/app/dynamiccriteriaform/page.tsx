'use client'

import { <PERSON>a<PERSON><PERSON>, FaPlus, FaUsers } from "react-icons/fa6";
import { useEffect, useState } from "react";
import { UserDto } from "../models/dtos";
import { PageSchema } from "@areldevops/rlx_shared/components";
import { useTranslations } from "use-intl";
import CreateCriteria from "./createcriteria";
import CriteriaTemplates from "./criteriatemplates";
import Reports from "./reports";
import AcademicReportCard from "./academicreportcard";
import SetCriteriaList from "./setcriterialist";
import DataConfirmation from "./dataconfirmation";
import PersonnelApprovalTable from "./personnelapprovaltable";
import PortfolioControl from "./portfoliocontrol";
import InputModalTest from "./inputmodaltest";
import FilterableDataTable from "./filterabledatatable";
import Units from "./units";

export default function DynamicCriteriaForm() {
    const [selectedUser, setSelectedUser] = useState<UserDto | undefined>();
    const t = useTranslations();

    return <PageSchema title={<> <FaList /> Organizasyon Yönetimi</>}>
        <Units />
    </PageSchema>
}