import React, { useState } from "react";

const data = [
  {
    sicilNo: "3218",
    unvan: "Prof. Dr.",
    adSoyad: "<PERSON> YILDIRIM",
    birim: "<PERSON><PERSON> Hastalıkları",
    ustBirim: "<PERSON><PERSON>i Tıp Bilimleri",
    durum: "onayBekliyor",
  },
  {
    sicilNo: "2887",
    unvan: "Doç. Dr.",
    adSoyad: "Abdullah <PERSON>",
    birim: "Yönetim Bilişim",
    ustBirim: "İktisadi ve İdari Bilimler Fakültesi",
    durum: "kontrolEdildi",
  },
  {
    sicilNo: "3172",
    unvan: "Dr. <PERSON>ğ<PERSON>",
    adSoyad: "<PERSON>",
    birim: "Genetik",
    ustBirim: "Fen Fakültesi",
    durum: "onaylaTikla",
  },
];

const durumBadge: any = {
  onayBekliyor: {
    text: "ONAY BEKLEYEN VERİLER VAR",
    class: "bg-yellow-500 text-white",
  },
  kontrolEdildi: {
    text: "VERİLER KONTROL EDİLDİ",
    class: "bg-green-500 text-white",
  },
  onaylaTikla: {
    text: "VERİYİ ONAYLAMAK İÇİN TIKLAYIN",
    class: "bg-blue-500 text-white cursor-pointer",
  },
};

export default function FilterableDataTable() {
  const [search, setSearch] = useState("");

  const filteredData = data.filter((row) =>
    Object.values(row)
      .join(" ")
      .toLowerCase()
      .includes(search.toLowerCase())
  );

  return (
    <div className="p-4">
      <input
        type="text"
        placeholder="Her şeyde ara..."
        className="mb-4 p-2 border rounded w-full md:w-1/3"
        value={search}
        onChange={(e) => setSearch(e.target.value)}
      />

      <div className="overflow-x-auto">
        <table className="min-w-full border border-gray-300">
          <thead className="bg-gray-100">
            <tr>
              <th className="border p-2">Sicil No</th>
              <th className="border p-2">Ünvan</th>
              <th className="border p-2">Ad Soyad</th>
              <th className="border p-2">Birim</th>
              <th className="border p-2">Üst Birim</th>
              <th className="border p-2">İşlem</th>
            </tr>
          </thead>
          <tbody>
            {filteredData.map((row, idx) => (
              <tr key={idx} className="hover:bg-gray-50">
                <td className="border p-2">{row.sicilNo}</td>
                <td className="border p-2">{row.unvan}</td>
                <td className="border p-2">{row.adSoyad}</td>
                <td className="border p-2">{row.birim}</td>
                <td className="border p-2">{row.ustBirim}</td>
                <td className="border p-2">
                  <span
                    className={`px-2 py-1 text-sm rounded ${durumBadge[row.durum].class}`}
                  >
                    {durumBadge[row.durum].text}
                  </span>
                </td>
              </tr>
            ))}
            {filteredData.length === 0 && (
              <tr>
                <td className="p-4 text-center text-gray-500" colSpan={6}>
                  Sonuç bulunamadı.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}
