'use client'

import { FaPlus, FaTrash } from "react-icons/fa";
import { FaUsers } from "react-icons/fa6";
import { useEffect, useState } from "react";
import { AutoCompleteDto } from "../models/dtos";
import { clientCallGet, clientCallPost } from "../helpers/clientcallhelper";
import { NoDataFound, PageModuleSchema } from "@areldevops/rlx_shared/components";
import { useTranslations } from "next-intl";

export default function CreateCriteria() {

    const t= useTranslations();
    return (
        <PageModuleSchema
            title={<><FaPlus /> {t("createCriteria")}</>}
            body={
                <>
                <div className="p-8 space-y-6">
                <h1 className="text-2xl font-bold">Yeni Dinamik Kriter Şablonu Oluşturma</h1>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <input type="text" placeholder="<PERSON>riter Adı" className="rlx-input-text" />
                    <input type="text" placeholder="Katsayı" className="rlx-input-text" />
                    <input type="text" placeholder="Kritere Girilecek Maksimum Kayıt Sayısı" className="rlx-input-text" />
                    <textarea placeholder="Açıklama" className="input md:col-span-2" rows={3}></textarea>
                    <select className="rlx-input-text">
                    <option>Statik üst kriter seçiniz</option>
                    </select>
                    <label className="flex items-center space-x-2">
                    <input type="checkbox" className="checkbox" />
                    <span>Onay bekleyen kriter</span>
                    </label>
                </div>

                <hr />

                <h2 className="text-xl font-semibold">Kriter Yetkilendirme</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <select className="rlx-input-text">
                    <option>Kriteri kimler veri ekleyebilir</option>
                    </select>
                    <select className="rlx-input-text">
                    <option>Kriteri kimler görebilir</option>
                    </select>
                </div>

                <div className="flex flex-wrap gap-2">
                    <button className="btn bg-green-600 rlx-button-edit text-white">Mevcut Kriterleri Göster</button>
                    <button className="btn bg-blue-600 rlx-button-save text-white">Kaydet</button>
                    <button className="btn bg-red-600 rlx-button-cancel text-white">İptal</button>
                </div>

                <hr />

                <h2 className="text-xl font-semibold">Inputlar</h2>
                <p className="text-sm">Input Bilgileri</p>

                {[1, 2].map((i) => (
                    <div key={i} className="flex justify-between items-start bg-gray-100 p-4 rounded shadow">
                    <div>
                        <p className="font-semibold">Test input {i} (sayı), Min: 12, Max: 20</p>
                        <label className="flex items-center space-x-2 mt-2">
                        <input type="checkbox" className="checkbox" />
                        <span>Çarpana katılacak mı ?</span>
                        </label>
                    </div>
                    <div className="space-x-2">
                        <button className="btn p-2 bg-purple-500 text-white rounded">⚙️</button>
                        <button className="btn p-2 bg-red-500 text-white rounded">🗑️</button>
                    </div>
                    </div>
                ))}

                <button className="btn bg-green-600 text-white mt-2">Input ekle</button>
                </div>
                </>
            } />



    );
}