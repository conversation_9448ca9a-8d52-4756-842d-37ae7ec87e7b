import React, { useState } from 'react';
import { Dialog } from '@headlessui/react';
import { FaEye } from 'react-icons/fa';

const sampleCertificates = [
  { id: 1, name: 'SERTIFIKA 1', date: '10/09/2025', place: 'AREL UNI', status: 'ONAYLANMADI' },
  { id: 2, name: 'SERTIFIKA 2', date: '10/09/2025', place: 'AREL UNI', status: 'ONAYLANMADI' },
  { id: 3, name: 'SERTIFIKA 3', date: '10/09/2025', place: 'AREL UNI', status: 'BEKLIYOR' },
  { id: 4, name: 'SERTIFIKA 4', date: '10/09/2025', place: 'AREL UNI', status: 'ONAYLANDI' },
];

const statusColors: any = {
  ONAYLANMADI: 'bg-gray-300 text-gray-700',
  BEKLIYOR: 'bg-yellow-200 text-yellow-800',
  ONAYLANDI: 'bg-green-200 text-green-800',
};

export default function DataConfirmation() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="p-6 space-y-6">
      <div className="space-y-2">
        <div>
          <strong>Adı Soyadı:</strong> Taylan Ekin KARA
        </div>
        <div>
          <strong>Fakülte - Bölüm / Anabilim Dalı:</strong> Yönetim Bilişim Sistemleri Geliştirme - Bilgi Teknolojileri Ofisi
        </div>
        <div>
          <strong>İdari Görevi (Varsa):</strong> Yazılım Geliştirme Uzmanı (Yönetim Bilişim Sistemleri Geliştirme) (Asil)
        </div>
        <div>
          <strong>Değerlendirme Skoru:</strong> 
        </div>
        <div>
          <strong>Ders Yükü:</strong> 0
        </div>
        <div>
          <strong>Verdiği Ders Adeti:</strong> 0
        </div>
      </div>

      <div className="mt-6 border p-4">
        <div className="flex justify-between items-center mb-2">
          <div className="font-semibold">7- Kurs, seminer veya çalıştaylara katılıp sertifika alma</div>
          <button
            onClick={() => setIsOpen(true)}
            className="rlx-button-edit"
          >
            KANITLARI GÖRÜNTÜLE
          </button>
        </div>

        <table className="rlx-table">
            <thead>
                <th>
                    <td>Onay Durumu</td>
                    <td>Sertifika Adı</td>
                    <td>Sertifika Tarihi</td>
                    <td>Sertifika Yeri</td>
                </th>
            </thead>
          </table>
      </div>

      <Dialog open={isOpen} onClose={() => setIsOpen(false)} className="relative z-50">
        <div className="fixed inset-0 bg-black/30" aria-hidden="true" />

        <div className="fixed inset-0 flex items-center justify-center p-4">
          <Dialog.Panel className="w-full max-w-2xl bg-white rounded p-4 space-y-4">
            <Dialog.Title className="text-lg font-bold mb-2">Kurs, seminer veya çalıştaylara katılım sertifikaları</Dialog.Title>

            {sampleCertificates.map((cert) => (
              <div
                key={cert.id}
                className="flex items-center justify-between border rounded p-2 mb-2 bg-gray-100"
              >
                <div className="flex items-center gap-4">
                  <span className={`px-2 py-1 rounded text-sm font-semibold ${statusColors[cert.status]}`}>
                    {cert.status}
                  </span>
                  <span>{cert.name}</span>
                  <span>{cert.date}</span>
                  <span>{cert.place}</span>
                </div>
                <button className="rlx-button-edit">
                  DOSYAYI GÖRÜNTÜLE
                </button>
              </div>
            ))}

            <div className="text-right">
              <button
                onClick={() => setIsOpen(false)}
                className="mt-4 px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
              >
                Kapat
              </button>
            </div>
          </Dialog.Panel>
        </div>
      </Dialog>
    </div>
  );
}
