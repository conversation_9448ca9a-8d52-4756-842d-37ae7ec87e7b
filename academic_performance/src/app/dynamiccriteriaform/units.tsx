import React, { useState, FC, ChangeEvent, useMemo } from "react";
import { FaChevronDown, FaChevronUp } from "react-icons/fa";
import { TreeListDto } from "../components/tree-list/TreeList.types";
import { TreeList } from "../components/tree-list/TreeList";
import { UnitForm } from "../components/unit-forms/UnitForm";
import { UnitSelectorModal } from "../components/unit-forms/UnitSelectorModal";

interface UnitDetailsProps {
  unit: TreeListDto;
}

export default function Units() {
  const [selectedUnit, setSelectedUnit] = useState<TreeListDto | null>(null);
  const [isNewMode, setIsNewMode] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");


  const menuData: TreeListDto[] = [
    {
      "id": "1",
      "name": "Campus",
      "code": "C001",
      "publicParentId": null,
      "children": [
        {
          "id": "1.1",
          "name": "Main Campus",
          "code": "MC001",
          "publicParentId": "1",
          "children": [
            {
              "id": "1.1.1",
              "name": "Engineering Faculty",
              "code": "ENG",
              "publicParentId": "1.1",
              "children": [
                {
                  "id": "1.1.1.1",
                  "name": "Computer Engineering Dept.",
                  "code": "CENG",
                  "publicParentId": "1.1.1",
                  "children": []
                },
                {
                  "id": "1.1.1.2",
                  "name": "Electrical Engineering Dept.",
                  "code": "EENG",
                  "publicParentId": "1.1.1",
                  "children": []
                }
              ]
            },
            {
              "id": "1.1.2",
              "name": "Science Faculty",
              "code": "SCI",
              "publicParentId": "1.1",
              "children": [
                {
                  "id": "1.1.2.1",
                  "name": "Physics Dept.",
                  "code": "PHYS",
                  "publicParentId": "1.1.2",
                  "children": []
                },
                {
                  "id": "1.1.2.2",
                  "name": "Chemistry Dept.",
                  "code": "CHEM",
                  "publicParentId": "1.1.2",
                  "children": []
                }
              ]
            }
          ]
        },
        {
          "id": "1.2",
          "name": "Branch Campus",
          "code": "BC001",
          "publicParentId": "1",
          "children": [
            {
              "id": "1.2.1",
              "name": "Business Faculty",
              "code": "BUS",
              "publicParentId": "1.2",
              "children": [
                {
                  "id": "1.2.1.1",
                  "name": "Management Dept.",
                  "code": "MGMT",
                  "publicParentId": "1.2.1",
                  "children": []
                },
                {
                  "id": "1.2.1.2",
                  "name": "Economics Dept.",
                  "code": "ECON",
                  "publicParentId": "1.2.1",
                  "children": []
                }
              ]
            }
          ]
        }
      ]
    }
  ];


  const handleSelectUnit = (unit: TreeListDto) => {
    if (!unit.children || unit.children.length === 0) {
      setSelectedUnit(unit);
      setIsNewMode(false);
    }
  };

  const handleNewClick = () => {
    setSelectedUnit(null);
    setIsNewMode(true);
  };


  const NewUnitForm: FC<{ onSave: () => void }> = ({ onSave }) => (
    <UnitForm
      unitTree={menuData} /> 
  );

  const UnitDetails: FC<UnitDetailsProps> = ({ unit }) => {
    const [openAccordion, setOpenAccordion] = useState<string | null>(null);
    const [filters, setFilters] = useState({
      position: "",
      department: "",
      startDate: "",
    });

    const toggleAccordion = (key: string) => {
      setOpenAccordion(openAccordion === key ? null : key);
    };

    const handleFilterChange = (
      e: ChangeEvent<HTMLInputElement>,
      key: keyof typeof filters
    ) => {
      setFilters({ ...filters, [key]: e.target.value });
    };

    const positions = [
      { position: "Software Engineer", department: "IT", startDate: "2023-05-01" },
      { position: "Project Manager", department: "Operations", startDate: "2022-01-15" },
    ];

    const filteredPositions = positions.filter((pos) => {
      return (
        pos.position.toLowerCase().includes(filters.position.toLowerCase()) &&
        pos.department.toLowerCase().includes(filters.department.toLowerCase()) &&
        pos.startDate.includes(filters.startDate)
      );
    });

    return (
      <div>
        <h2 className="text-xl font-bold mb-4">{unit.name}</h2>

        <div className="border rounded mb-4">
          <div
            className="p-4 bg-gray-100 cursor-pointer flex justify-between items-center"
            onClick={() => toggleAccordion("address")}
          >
            <span>Address Info</span>
            {openAccordion === "address" ? <FaChevronUp /> : <FaChevronDown />}
          </div>
          {openAccordion === "address" && (
            <div className="p-4">
              <table className="w-full text-sm border">
                <thead className="bg-gray-200">
                  <tr>
                    <th className="border p-2">Title</th>
                    <th className="border p-2">Detail</th>
                    <th className="border p-2">Postal Code</th>
                    <th className="border p-2">District</th>
                    <th className="border p-2">City</th>
                    <th className="border p-2">Country</th>
                    <th className="border p-2">Default</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td className="border p-20">{unit.name}</td>
                    <td className="border p-2">Erguvan Sokak No:26</td>
                    <td className="border p-2">34537</td>
                    <td className="border p-2">Büyükçekmece</td>
                    <td className="border p-2">İstanbul</td>
                    <td className="border p-2">Turkey</td>
                    <td className="border p-2 text-center">✓</td>
                  </tr>
                </tbody>
              </table>
            </div>
          )}
        </div>

        <div className="border rounded">
          <div
            className="p-4 bg-gray-100 cursor-pointer flex justify-between items-center"
            onClick={() => toggleAccordion("positions")}
          >
            <span>Associated Positions</span>
            {openAccordion === "positions" ? <FaChevronUp /> : <FaChevronDown />}
          </div>
          {openAccordion === "positions" && (
            <div className="p-4 space-y-2">
              <div className="grid grid-cols-3 gap-2 mb-2">
                <input
                  type="text"
                  placeholder="Filter by Position"
                  value={filters.position}
                  onChange={(e) => handleFilterChange(e, "position")}
                  className="border p-2"
                />
                <input
                  type="text"
                  placeholder="Filter by Department"
                  value={filters.department}
                  onChange={(e) => handleFilterChange(e, "department")}
                  className="border p-2"
                />
                <input
                  type="text"
                  placeholder="Filter by Start Date"
                  value={filters.startDate}
                  onChange={(e) => handleFilterChange(e, "startDate")}
                  className="border p-2"
                />
              </div>
              <table className="w-full text-sm border">
                <thead className="bg-gray-200">
                  <tr>
                    <th className="border p-2">Position</th>
                    <th className="border p-2">Department</th>
                    <th className="border p-2">Start Date</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredPositions.map((pos, i) => (
                    <tr key={i}>
                      <td className="border p-2">{pos.position}</td>
                      <td className="border p-2">{pos.department}</td>
                      <td className="border p-2">{pos.startDate}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="flex h-full">
      <aside className="w-1/3 p-4 border-r overflow-y-auto">
        <h2 className="text-xl font-bold mb-4">Units</h2>
        <input
          type="text"
          placeholder="Birim ara..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="rlx-input-text"
        />
        <TreeList
          data={menuData}
          onSelect={handleSelectUnit}
          searchTerm={searchTerm}
          selectedId={selectedUnit?.id}
           />
      </aside>

      <main className="w-2/3 p-6 overflow-y-auto">
        <div className="flex justify-end mb-4">
          <button
            onClick={handleNewClick}
            className="bg-emerald-700 text-white px-4 py-2 rounded"
          >
            + Yeni Birim Ekle
          </button>
        </div>

        {selectedUnit ? (
          <UnitDetails unit={selectedUnit} />
        ) : isNewMode ? (
          <NewUnitForm onSave={() => setIsNewMode(false)} />
        ) : (
          <div className="text-center text-gray-400 text-lg">
            Select a unit or add a new one.
          </div>
        )}
      </main>
    </div>
  );
}
