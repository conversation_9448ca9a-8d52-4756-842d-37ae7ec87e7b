import React from 'react';
import { FaExternalLinkAlt } from 'react-icons/fa';

const userInfo = {
  sicilNo: '2887',
  unvan: 'Do<PERSON>. Dr.',
  adSoyad: '<PERSON>',
  yerleske: '<PERSON><PERSON>özükara Yerleşkesi',
  departman: 'İkt<PERSON>di ve İdari Bilimler Fakültesi',
  birim: '<PERSON>önetim Bilişim Si<PERSON>mleri',
  gorev: '<PERSON><PERSON><PERSON><PERSON> (Yönetim Bilişim <PERSON>) (Asil)',
  email: '<EMAIL>',
  dahili: '1313',
  telefon: '05425994416',
  baslangic: '2022-05-23',
};

const sampleCourses = [
  {
    donem: '2024-2025-Güz',
    dersKodu: '7MVD51207',
    dersAdi: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  },
  {
    donem: '2024-2025-Gü<PERSON>',
    dersKodu: '7ISL51800',
    dersAdi: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  },
  {
    donem: '2024-2025-G<PERSON><PERSON>',
    dersKodu: 'YBSL393',
    dersAdi: 'Yöneylem Araştırması I',
  },
  // Devamı için aynı yapıyı çoğaltabilirsiniz...
];

export default function PortfolioControl() {
  return (
    <div className="p-4 space-y-4">
      <h1 className="text-xl font-semibold mb-4">Portfolyo</h1>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="border rounded p-4 space-y-2 bg-white shadow-sm">
          <img
            src="https://arelim.arel.edu.tr/logo502.png"
            alt="Profil"
            className="w-32 h-32 object-cover rounded mx-auto"
          />
          <div><strong>Sicil No:</strong> {userInfo.sicilNo}</div>
          <div><strong>Ünvan:</strong> {userInfo.unvan}</div>
          <div><strong>Adı Soyadı:</strong> {userInfo.adSoyad}</div>
          <div><strong>Yerleşke:</strong> {userInfo.yerleske}</div>
          <div><strong>Departman:</strong> {userInfo.departman}</div>
          <div><strong>Birim:</strong> {userInfo.birim}</div>
          <div><strong>Görev:</strong> {userInfo.gorev}</div>
          <div><strong>E-Posta:</strong> {userInfo.email}</div>
          <div><strong>Dahili:</strong> {userInfo.dahili}</div>
          <div><strong>Telefon No:</strong> {userInfo.telefon}</div>
          <div><strong>İş Başlangıç Tarihi:</strong> {userInfo.baslangic}</div>
        </div>

        <div className="md:col-span-2 overflow-auto">
          <table className="w-full table-auto border border-gray-200 text-sm">
            <thead className="bg-gray-100">
              <tr>
                <th className="p-2 border">Dönem</th>
                <th className="p-2 border">Ders Kodu</th>
                <th className="p-2 border">Ders Adı</th>
                <th className="p-2 border">Sınav Kağıtları</th>
                <th className="p-2 border">Yanıt Anahtarı</th>
                <th className="p-2 border">Sınav Tutanağı</th>
                <th className="p-2 border">Yoklama Kağıdı</th>
                <th className="p-2 border">Ders İzlencesi</th>
                <th className="p-2 border">14 Haftalık Ders Yoklama Listeleri</th>
                <th className="p-2 border">Bütünleme Not Çizelgesi</th>
                <th className="p-2 border">UZEM Sistemi'nde YOK</th>
                <th className="p-2 border">İşlem</th>
              </tr>
            </thead>
            <tbody>
              {sampleCourses.map((course, index) => (
                <tr key={index} className="text-center">
                  <td className="p-2 border">{course.donem}</td>
                  <td className="p-2 border">{course.dersKodu}</td>
                  <td className="p-2 border">{course.dersAdi}</td>
                  {Array.from({ length: 8 }).map((_, i) => (
                    <td key={i} className="p-2 border">Bekliyor</td>
                  ))}
                  <td className="p-2 border">
                    <button className="rlx-button-edit">
                      <FaExternalLinkAlt />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
