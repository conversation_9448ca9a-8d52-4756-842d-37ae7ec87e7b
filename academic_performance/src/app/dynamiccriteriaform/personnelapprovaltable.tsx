import React from 'react';

const data = [
  {
    id: 1,
    sicilNo: '3218',
    unvan: 'Prof. Dr.',
    adSoyad: '<PERSON> YILDIRIM',
    birimAdi: '<PERSON>hil<PERSON> Tıp Bilimleri / <PERSON><PERSON> Hastalıkları Anabilim Dalı',
    ustBirim: '<PERSON><PERSON><PERSON> Tıp Bilimleri Bölümü',
    veriDurumu: 'ONAY BEKLEYEN VERİLER VAR',
    veriOnay: 'VERİ ONAYI İÇİN TIKLAYINIZ',
  },
];

export default function PersonnelApprovalTable() {
  return (
    <div className="p-4">
      <div className="flex items-center gap-4 mb-4">
        <select className="border p-2 rounded">
          <option>Tümü</option>
        </select>
        <select className="border p-2 rounded">
          <option>Dönem</option>
        </select>
      </div>

      <div className="overflow-x-auto border rounded">
        <table className="min-w-full text-sm text-left">
          <thead className="bg-blue-100">
            <tr>
              <th className="p-2 border">Sicil No</th>
              <th className="p-2 border"><PERSON><PERSON><PERSON></th>
              <th className="p-2 border">Ad Soyad</th>
              <th className="p-2 border">Birim Adı</th>
              <th className="p-2 border">Üst Birim Adı</th>
              <th className="p-2 border">İşlemler</th>
            </tr>
          </thead>
          <tbody>
            {data.map((person) => (
              <tr key={person.id} className="even:bg-gray-100">
                <td className="p-2 border">{person.sicilNo}</td>
                <td className="p-2 border">{person.unvan}</td>
                <td className="p-2 border">{person.adSoyad}</td>
                <td className="p-2 border">{person.birimAdi}</td>
                <td className="p-2 border">{person.ustBirim}</td>
                <td className="p-2 border space-y-1">
                  <div className="text-xs bg-yellow-300 px-2 py-1 rounded">
                    {person.veriDurumu}
                  </div>
                  <button className="rlx-button-edit">
                    {person.veriOnay}
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="flex items-center justify-between mt-4">
        <div className="flex items-center gap-2">
          <span>Size</span>
          <select className="border rounded p-1">
            <option>20</option>
          </select>
        </div>
        <div className="flex items-center gap-2">
          <span>Page</span>
          <select className="border rounded p-1">
            <option>1</option>
          </select>
          <span>Total Page(s) 1 | Total Item(s) 1</span>
        </div>
      </div>
    </div>
  );
}
