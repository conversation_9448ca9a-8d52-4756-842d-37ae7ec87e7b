export interface ClaimSchemaDto {
    id: string
    claimType: string
    claimValue: string
    description: string
    disabled: boolean
}

export interface RoleDto {
    id: string
    name: string
    disabled: boolean
}

export interface UserDto {
    id: string
    userName: string
    email: string
    phoneNumber: string
    fullName: string
    password: string
    disabled: boolean
}

export interface AutoCompleteDto {
    id: string
    name: string
}