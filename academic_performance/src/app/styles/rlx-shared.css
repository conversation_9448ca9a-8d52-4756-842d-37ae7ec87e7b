@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  @apply text-sm font-normal text-gray-500 bg-white;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .rlx-full .rlx-logo {
    @apply text-3xl;
  }

  .rlx-lite .rlx-logo {
    @apply text-xl text-center;
  }

  .rlx-lite .rlx-app-name {
    @apply hidden;
  }

  .rlx-lite .rlx-nav-header {
    @apply justify-center;
  }

  .rlx-lite #rlx-nav-toggle {
    /* @apply mx-auto; */
  }

  .rlx-lite .rlx-nav-link-text {
    @apply hidden;
  }

  .rlx-lite .rlx-nav-link-icon {
    @apply text-2xl;
  }

  .rlx-lite .rlx-main {
    @apply ml-16;
  }

  .rlx-lite .rlx-nav {
    @apply w-16 min-w-16;
  }

  .rlx-full .rlx-nav {
    @apply w-64 min-w-64;
  }

  .rlx-full .rlx-header {
    @apply left-64;
  }

  .rlx-lite .rlx-header {
    @apply left-16;
  }

  @media screen and (max-width: 768px) {
    .rlx-lite .rlx-nav {
      @apply w-0 min-w-0 overflow-hidden;
    }

    .rlx-lite .rlx-header {
      @apply left-0;
    }

    .rlx-lite .rlx-main {
      @apply ml-0;
    }

    .rlx-full .rlx-main {
      @apply ml-0;
    }

    /* .rlx-full .rlx-username{
      @apply hidden;
    } */
  }
}

.rlx-header {
  @apply transition-all duration-500 flex items-center fixed top-0 right-0 bg-gray-100 h-16 p-4 text-gray-600 gap-4;
}

.rlx-main {
  @apply transition-all duration-500 flex-1 ml-64;
}

.rlx-font-geistvf {
  font-family: "GeistVF";
}

.rlx-input-text {
  @apply block border border-gray-300 hover:border-gray-400 rounded-md shadow-sm w-full px-2 py-1 text-sm rlx-font-geistvf;
}

.rlx-form-label {
  @apply flex items-center gap-2 block text-sm font-normal text-gray-500 mb-1;
}

.rlx-page-title {
  @apply text-lg font-semibold mb-4 px-4 py-2 text-gray-600 border-b flex items-center gap-2;
}

.rlx-page-module {
  @apply border border-gray-200 rounded-md shadow-sm bg-gray-50;
}

.rlx-page-module-header {
  @apply bg-gray-100 flex justify-between text-gray-800;
}

.rlx-page-module-title {
  @apply text-sm font-semibold px-4 py-2 flex items-center gap-2 text-gray-600;
}

.rlx-page-module-actions {
  @apply text-base font-normal px-4 flex items-center gap-2;
}

.rlx-page-module-body {
  @apply p-4;
}

.rlx-form {
  @apply grid grid-cols-1 md:grid-cols-3 gap-4 p-4 shadow-md;
}

.rlx-button-actions {
  @apply flex items-center gap-2 rounded-md px-2 py-1 text-xs text-sky-600 border border-sky-600 bg-white hover:bg-sky-700 hover:text-sky-50;
}

.rlx-button-save {
  @apply flex items-center gap-2 bg-emerald-700 hover:bg-emerald-800 text-emerald-50 font-semibold text-sm py-2 px-10 rounded;
}

.rlx-button-save-sm {
  @apply flex items-center gap-2 bg-emerald-700 hover:bg-emerald-800 text-emerald-50 font-semibold text-sm px-2 py-1 rounded;
}

.rlx-button-cancel {
  @apply flex items-center gap-2 bg-stone-200 hover:bg-stone-300 text-stone-600 font-semibold text-sm py-2 px-4 rounded;
}
.rlx-button-cancel-sm {
  @apply flex items-center gap-2 bg-stone-200 hover:bg-stone-300 text-stone-600 font-semibold text-sm px-2 py-1 rounded;
}

.rlx-button-delete {
  @apply flex items-center gap-2 rounded-md px-2 py-1 text-xs text-rose-500 border border-rose-500 bg-white hover:bg-rose-700 hover:text-rose-50;
}

.rlx-button-edit {
  @apply flex items-center gap-2 rounded-md px-2 py-1 text-xs text-sky-600 border border-sky-600 bg-white hover:bg-sky-700 hover:text-sky-50;
}

.rlx-table {
  @apply table-auto w-full text-left rounded-md  shadow-sm;
}

.rlx-table td {
  @apply border border-gray-200 px-2 py-2 ;
}

.rlx-table th {
  @apply border border-gray-200  px-2 py-2;
}



/* .rlx-table tr:last-child td {
  @apply border-none;
} */

.rlx-table tbody tr:hover {
  @apply bg-gray-100;
}

.rlx-table td:last-child {
  /* @apply  flex justify-end gap-2; */
}

.rlx-table th:last-child {
  /* @apply   flex justify-end; */
}

.rlx-table th {
  @apply font-semibold;
}

.rlx-table-button-group {
  @apply flex justify-end gap-2;
}

.rlx-table-button-group-left {
  @apply flex justify-start gap-2;
}

.rlx-table thead tr:last-child td {
  @apply border-b-4;
}

.rlx-table thead tr:last-child th {
  @apply border-b-4;
}

.rlx-nav {
  @apply transition-all duration-500 bg-teal-800 text-teal-50 min-h-screen rounded-br-3xl shadow-md fixed;
}

.rlx-nav-list li {
  @apply px-2 py-2;
}

.rlx-nav-list li:hover {
  @apply bg-teal-100 rounded-md text-teal-800;
}

.rlx-nav-link {
  @apply flex items-center gap-2 font-semibold;
}

.vhr {
  @apply w-[1px] h-full bg-gray-300;
}

.rlx-logo {
  @apply flex items-center gap-2;
}

.rlx-error-message {
  @apply text-rose-600 text-xs m-1 italic block;
}

.rlx-modal-container {
  @apply fixed inset-0 flex items-center justify-center bg-black bg-opacity-50;
}

.rlx-modal {
  @apply bg-white p-6 rounded shadow-lg;
}

.rlx-modal-header {
  @apply flex gap-2 mb-4 relative;
}

.rlx-modal-title {
  @apply flex text-lg gap-2 items-center font-semibold;
}

.rlx-modal-close {
  @apply absolute top-[-32px] right-[-32px];
}

.rlx-modal-close-button {
  @apply bg-rose-600 text-white px-3 py-2 rounded hover:bg-rose-500;
}

.rlx-modal-body {
  @apply gap-4 mb-4;
}

.rlx-modal-footer {
  @apply flex items-center;
}

.rlx-modal-button-rose {
  @apply flex items-center gap-2 rounded-md px-2 py-1 text-sm hover:text-rose-500 border hover:border-rose-500 hover:bg-white bg-rose-700 text-rose-50;
}

.rlx-modal-button-gray {
  @apply flex items-center gap-2 rounded-md px-2 py-1 text-sm bg-gray-300 text-gray-600 p-1 rounded hover:bg-gray-600 hover:text-gray-300;
}

.flex-all-center {
  @apply flex items-center justify-center;
}

.rlx-app-list {
  @apply fixed right-4 mt-4 bg-white border rounded-md shadow-lg;
}

.rlx-app-list ul {
  @apply flex gap-2 shadow-md h-96 overflow-y-auto w-96 flex-wrap p-2;
}

.rlx-app-list ul li {
  @apply flex-1 items-center p-4;
}

.rlx-app-list ul li a {
  @apply flex flex-col items-center text-center justify-center gap-2;
}

.rlx-app-list ul li a svg {
  @apply text-3xl;
}

.rlx-user-menu {
  @apply absolute right-0 mt-2 bg-white border rounded-md shadow-lg w-48 flex flex-col;
}

.rlx-user-menu ul li a {
  @apply flex items-center  gap-2 p-2;
}

.rlx-lang-btn {
  @apply ml-auto flex items-center gap-2 text-gray-700 font-semibold;
}

.rlx-lang-list {
  @apply fixed  mt-4 bg-white border rounded-md shadow-lg;
}

.rlx-lang-list ul {
  @apply gap-2 shadow-md w-48  p-2;
}

.rlx-lang-list ul li {
  @apply items-center p-2;
}

.rlx-lang-list ul li:hover {
  @apply bg-gray-100;
}

.rlx-lang-list ul li a {
  /* @apply flex flex-col items-center text-center justify-center gap-2; */
}

.rlx-lang-list ul li a svg {
  /* @apply text-3xl; */
}
