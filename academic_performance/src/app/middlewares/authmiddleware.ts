import { NextRequest, NextResponse } from 'next/server';
import { getCookie } from '@areldevops/rlx_shared/helpers';
import { appConfig } from '../appconfig';
export function authMiddleware(req: NextRequest) {
    // const token = getCookie(req, 'rlx-access-token');
    // if (!token) {
    //     return NextResponse.redirect(appConfig.loginUrl);
    // }
    return NextResponse.next();
}
export const config = {
    matcher: ['/x'],
};
